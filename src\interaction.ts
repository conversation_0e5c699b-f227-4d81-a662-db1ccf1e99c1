export const EVENT_BEFORE_INTERACT = "beforeInteract";
export const EVENT_INTERACT = "interact";
export const EVENT_AFTER_INTERACT = "afterInteract";

export type HandlerControl = "abortEvent" | "abortAll";
export type EventHandler = () =>
  | HandlerControl
  | void
  | Promise<HandlerControl | void>;

export class Interaction {
  events: string[] = [
    EVENT_BEFORE_INTERACT,
    EVENT_INTERACT,
    EVENT_AFTER_INTERACT,
  ];
  eventHandlers: { [key: string]: Function[] } = {
    [EVENT_BEFORE_INTERACT]: [],
    [EVENT_INTERACT]: [],
    [EVENT_AFTER_INTERACT]: [],
  };

  addBeforeEvent(beforeEvent: string, newEvent: string) {
    this.events.splice(this.events.indexOf(beforeEvent) + 1, 0, newEvent);
    this.eventHandlers[newEvent] = [];
  }

  addAfterEvent(afterEvent: string, newEvent: string) {
    this.events.splice(this.events.indexOf(afterEvent) + 1, 0, newEvent);
    this.eventHandlers[newEvent] = [];
  }

  removeEvent(event: string) {
    if (!this.events.includes(event)) {
      throw new Error(`Event ${event} does not exist`);
    }
    this.events = this.events.filter((e) => e !== event);
    delete this.eventHandlers[event];
  }

  addHandler(event: string, handler: Function) {
    if (!this.events.includes(event)) {
      throw new Error(`Event ${event} does not exist`);
    }
    this.eventHandlers[event].push(handler);
  }

  removeHandler(event: string, handler: Function) {
    if (!this.events.includes(event)) {
      throw new Error(`Event ${event} does not exist`);
    }
    this.eventHandlers[event] = this.eventHandlers[event].filter(
      (h) => h !== handler
    );
  }

  async interact() {
    for (const event of this.events) {
      for (const handler of this.eventHandlers[event]) {
        const result = await handler();

        if (result === "abortEvent") {
          break;
        }
        if (result === "abortAll") {
          return;
        }
      }
    }
  }
}
