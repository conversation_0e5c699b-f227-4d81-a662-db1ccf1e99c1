hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@rollup/rollup-android-arm-eabi@4.45.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.45.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.45.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.45.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.45.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.45.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.45.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.45.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.45.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.45.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.45.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.45.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.45.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.45.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.45.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.45.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.45.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.45.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.45.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.45.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/estree@1.0.8':
    '@types/estree': private
  esbuild@0.25.6:
    esbuild: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fsevents@2.3.3:
    fsevents: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  rollup@4.45.0:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  tinyglobby@0.2.14:
    tinyglobby: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Sat, 12 Jul 2025 02:16:58 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@rollup/rollup-android-arm-eabi@4.45.0'
  - '@rollup/rollup-android-arm64@4.45.0'
  - '@rollup/rollup-darwin-arm64@4.45.0'
  - '@rollup/rollup-darwin-x64@4.45.0'
  - '@rollup/rollup-freebsd-arm64@4.45.0'
  - '@rollup/rollup-freebsd-x64@4.45.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.0'
  - '@rollup/rollup-linux-arm64-gnu@4.45.0'
  - '@rollup/rollup-linux-arm64-musl@4.45.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.0'
  - '@rollup/rollup-linux-riscv64-musl@4.45.0'
  - '@rollup/rollup-linux-s390x-gnu@4.45.0'
  - '@rollup/rollup-linux-x64-gnu@4.45.0'
  - '@rollup/rollup-linux-x64-musl@4.45.0'
  - '@rollup/rollup-win32-arm64-msvc@4.45.0'
  - '@rollup/rollup-win32-ia32-msvc@4.45.0'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\tots\node_modules\.pnpm
virtualStoreDirMaxLength: 60
